"use client";

import { <PERSON><PERSON>ircle, Globe, Shield } from "lucide-react";
import Image from "next/image";

export function EnhancedVerificationHeader() {
  return (
    <header className="relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-yellow-50 to-red-50 opacity-50"></div>
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      ></div>

      <div className="relative z-10 text-center py-12">
        {/* Main Header */}
        <div className="flex items-center justify-center mb-8">
          <div className="w-20 h-20 relative mr-6 drop-shadow-lg">
            <Image
              src="/images/armoirie.png"
              alt="Armoirie de la République de Guinée"
              fill
              className="object-contain"
            />
          </div>
          <div className="text-left">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
              RÉPUBLIQUE DE GUINÉE
            </h1>
            <p className="text-lg text-gray-600 font-medium">
              Travail - Justice - Solidarité
            </p>
          </div>
        </div>

        {/* Verification System Info */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
            <div className="flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-green-600 mr-3" />
              <h2 className="text-2xl md:text-3xl font-bold text-green-700">
                Système de Vérification Officiel
              </h2>
            </div>

            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              Plateforme sécurisée de vérification d'authenticité des
              certificats de résidence de la République de Guinée
            </p>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-6">
              <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-xl border border-green-200">
                <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-green-800">
                    Vérification Instantanée
                  </h3>
                  <p className="text-sm text-green-700">
                    Résultats en temps réel
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                <Shield className="w-6 h-6 text-yellow-600 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-yellow-800">
                    Sécurité Renforcée
                  </h3>
                  <p className="text-sm text-yellow-700">
                    Cryptographie avancée
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-xl border border-red-200">
                <Globe className="w-6 h-6 text-red-600 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-red-800">
                    Accès Universel
                  </h3>
                  <p className="text-sm text-red-700">24h/24, 7j/7</p>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap items-center justify-center gap-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span>Système en ligne</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <span>Connexion sécurisée HTTPS</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Certifié par l'État</span>
              </div>
            </div>
          </div>
        </div>

        {/* Tricolor Band - Enhanced */}
        <div className="max-w-2xl mx-auto">
          <div className="flex h-4 rounded-full overflow-hidden shadow-lg">
            <div className="flex-1 bg-gradient-to-r from-red-600 to-red-500"></div>
            <div className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-300"></div>
            <div className="flex-1 bg-gradient-to-r from-green-600 to-green-500"></div>
          </div>
          <p className="text-xs text-gray-500 mt-2 font-medium">
            Couleurs nationales de la République de Guinée
          </p>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 max-w-3xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/30">
              <div className="text-2xl font-bold text-green-600">100%</div>
              <div className="text-sm text-gray-600">Sécurisé</div>
            </div>
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/30">
              <div className="text-2xl font-bold text-yellow-600">24/7</div>
              <div className="text-sm text-gray-600">Disponible</div>
            </div>
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/30">
              <div className="text-2xl font-bold text-red-600">Instant</div>
              <div className="text-sm text-gray-600">Vérification</div>
            </div>
            <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/30">
              <div className="text-2xl font-bold text-blue-600">Officiel</div>
              <div className="text-sm text-gray-600">Gouvernement</div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
