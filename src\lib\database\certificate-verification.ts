import { createAdminClient } from "@/lib/server/appwrite";
import {
  ADDRESSES_COLLECTION_ID,
  CERTIFICATE_METADATA_COLLECTION_ID,
  CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
  CERTIFICATES_COLLECTION_ID,
  CITIZENS_COLLECTION_ID,
  DATABASE_ID,
} from "@/lib/server/database";
import { ID, Query } from "node-appwrite";

// Interface for metadata (separate collection)
export interface CertificateMetadata {
  $id: string;
  verificationId: string; // Reference to certificate_verifications
  issuerType: string; // 'chef' | 'agent' | 'admin'
  issuerId: string;
  issuerName: string;
  region: string;
  commune: string;
  quartier: string;
  // Optional fields for future extensions
  revocationReason?: string;
  revokedAt?: string;
  revokedBy?: string;
  createdAt: string;
  updatedAt: string;
}

// Interface for verifications (without complex metadata)
export interface CertificateVerification {
  $id: string;
  hash: string;
  certificateId: string;
  citizenId: string;
  issuedAt: string;
  expiresAt: string;
  isValid: boolean;
  isRevoked: boolean;
  verificationCount: number;
  lastVerifiedAt?: string;
  metadataId: string; // Reference to certificate_verifications_metadata
  createdAt: string;
  updatedAt: string;
}

// Combined interface for application use
export interface CertificateVerificationWithMetadata
  extends CertificateVerification {
  metadata: CertificateMetadata;
}

export class CertificateVerificationService {
  private static async getDatabase() {
    const { databases } = await createAdminClient();
    return databases;
  }

  /**
   * Creates metadata record for a certificate verification
   */
  private static async createMetadata(
    verificationId: string,
    metadataData: {
      issuerType: string;
      issuerId: string;
      issuerName: string;
      region: string;
      commune: string;
      quartier: string;
    }
  ): Promise<CertificateMetadata> {
    const databases = await this.getDatabase();

    const metadata: Omit<CertificateMetadata, "$id"> = {
      verificationId,
      issuerType: metadataData.issuerType,
      issuerId: metadataData.issuerId,
      issuerName: metadataData.issuerName,
      region: metadataData.region,
      commune: metadataData.commune,
      quartier: metadataData.quartier,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return (await databases.createDocument(
      DATABASE_ID,
      CERTIFICATE_METADATA_COLLECTION_ID,
      ID.unique(),
      metadata
    )) as unknown as CertificateMetadata;
  }

  /**
   * Gets metadata by verification ID
   */
  private static async getMetadataByVerificationId(
    verificationId: string
  ): Promise<CertificateMetadata | null> {
    try {
      const databases = await this.getDatabase();

      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_METADATA_COLLECTION_ID,
        [Query.equal("verificationId", verificationId), Query.limit(1)]
      );

      return documents.length > 0
        ? (documents[0] as unknown as CertificateMetadata)
        : null;
    } catch (error) {
      console.error("Error retrieving metadata:", error);
      return null;
    }
  }

  /**
   * Creates a verification record for a certificate
   */
  static async createVerification(data: {
    hash: string;
    certificateId: string;
    citizenId: string;
    issuedAt: Date;
    expiresAt: Date;
    metadata: {
      issuerType: string;
      issuerId: string;
      issuerName: string;
      region: string;
      commune: string;
      quartier: string;
    };
  }): Promise<CertificateVerificationWithMetadata> {
    const databases = await this.getDatabase();

    try {
      // First, create the verification record with a placeholder metadataId
      const verificationData: Omit<CertificateVerification, "$id"> = {
        hash: data.hash,
        certificateId: data.certificateId,
        citizenId: data.citizenId,
        issuedAt: data.issuedAt.toISOString(),
        expiresAt: data.expiresAt.toISOString(),
        isValid: true,
        isRevoked: false,
        verificationCount: 0,
        metadataId: "", // Will be updated after metadata creation
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const verification = (await databases.createDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        ID.unique(),
        verificationData
      )) as unknown as CertificateVerification;

      // Then create the metadata record
      const metadata = await this.createMetadata(
        verification.$id,
        data.metadata
      );

      // Update the verification with the metadata ID
      const updatedVerification = (await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        verification.$id,
        {
          metadataId: metadata.$id,
          updatedAt: new Date().toISOString(),
        }
      )) as unknown as CertificateVerification;

      return {
        ...updatedVerification,
        metadata,
      };
    } catch (error) {
      console.error("Error creating verification:", error);
      throw error;
    }
  }

  /**
   * Gets a verification by hash with metadata
   */
  static async getVerificationByHash(
    hash: string
  ): Promise<CertificateVerificationWithMetadata | null> {
    try {
      const databases = await this.getDatabase();

      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        [Query.equal("hash", hash), Query.limit(1)]
      );

      if (documents.length === 0) {
        return null;
      }

      const verification = documents[0] as unknown as CertificateVerification;
      const metadata = await this.getMetadataByVerificationId(verification.$id);

      if (!metadata) {
        console.error(
          `Metadata not found for verification ${verification.$id}`
        );
        return null;
      }

      return {
        ...verification,
        metadata,
      };
    } catch (error) {
      console.error("Error retrieving verification:", error);
      return null;
    }
  }

  /**
   * Gets non-sensitive citizen information for verification display
   */
  static async getCitizenInfoForVerification(
    citizenId: string,
    certificateId: string
  ): Promise<{
    fullName: string;
    profession: string;
    nationality: string;
    residenceLocation: string;
    certificateType: string;
    purpose: string;
    validityPeriod: string;
  } | null> {
    try {
      const databases = await this.getDatabase();

      // Get citizen information (non-sensitive fields only)
      const { documents: citizenDocs } = await databases.listDocuments(
        DATABASE_ID,
        CITIZENS_COLLECTION_ID,
        [Query.equal("userId", citizenId), Query.limit(1)]
      );

      if (citizenDocs.length === 0) {
        return null;
      }

      const citizen = citizenDocs[0];

      // Get certificate information for purpose and type
      const certificate = await databases.getDocument(
        DATABASE_ID,
        CERTIFICATES_COLLECTION_ID,
        certificateId
      );

      // Get address information (general location only)
      const { documents: addressDocs } = await databases.listDocuments(
        DATABASE_ID,
        ADDRESSES_COLLECTION_ID,
        [Query.equal("citizenId", citizenId), Query.limit(1)]
      );

      const address = addressDocs.length > 0 ? addressDocs[0] : null;

      return {
        fullName: `${citizen.prenom} ${citizen.nom}`,
        profession: citizen.profession || "Non spécifiée",
        nationality: citizen.nationalite || "Guinéenne",
        residenceLocation: address
          ? `${address.quartier}, ${address.commune}, ${address.region}`
          : "Non spécifiée",
        certificateType: "Certificat de Résidence",
        purpose: certificate.motif || "Non spécifié",
        validityPeriod: "3 mois",
      };
    } catch (error) {
      console.error("Error getting citizen info for verification:", error);
      return null;
    }
  }

  /**
   * Updates verification count
   */
  static async incrementVerificationCount(hash: string): Promise<void> {
    try {
      const verification = await this.getVerificationByHash(hash);
      if (!verification) return;

      const databases = await this.getDatabase();

      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        verification.$id,
        {
          verificationCount: verification.verificationCount + 1,
          lastVerifiedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Error updating verification count:", error);
    }
  }

  /**
   * Revokes a certificate
   */
  static async revokeCertificate(
    hash: string,
    reason?: string
  ): Promise<boolean> {
    try {
      const verification = await this.getVerificationByHash(hash);
      if (!verification) return false;

      const databases = await this.getDatabase();

      // Update verification record
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        verification.$id,
        {
          isRevoked: true,
          isValid: false,
          updatedAt: new Date().toISOString(),
        }
      );

      // Update metadata with revocation info
      await databases.updateDocument(
        DATABASE_ID,
        CERTIFICATE_METADATA_COLLECTION_ID,
        verification.metadata.$id,
        {
          revocationReason: reason || "Revoked by administration",
          revokedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      return true;
    } catch (error) {
      console.error("Error revoking certificate:", error);
      return false;
    }
  }

  /**
   * Validates if a certificate is valid
   */
  static async validateCertificate(hash: string): Promise<{
    isValid: boolean;
    status: "valid" | "expired" | "revoked" | "not_found";
    verification?: CertificateVerificationWithMetadata;
    message: string;
  }> {
    const verification = await this.getVerificationByHash(hash);

    if (!verification) {
      return {
        isValid: false,
        status: "not_found",
        message: "Certificate not found in database",
      };
    }

    // Increment verification count
    await this.incrementVerificationCount(hash);

    if (verification.isRevoked) {
      return {
        isValid: false,
        status: "revoked",
        verification,
        message: "This certificate has been revoked",
      };
    }

    const now = new Date();
    const expiresAt = new Date(verification.expiresAt);

    if (now > expiresAt) {
      return {
        isValid: false,
        status: "expired",
        verification,
        message: "This certificate has expired",
      };
    }

    return {
      isValid: true,
      status: "valid",
      verification,
      message: "Valid certificate",
    };
  }

  /**
   * Gets verification statistics
   */
  static async getVerificationStats(certificateId?: string): Promise<{
    totalVerifications: number;
    validCertificates: number;
    expiredCertificates: number;
    revokedCertificates: number;
  }> {
    try {
      const databases = await this.getDatabase();

      const queries = certificateId
        ? [Query.equal("certificateId", certificateId)]
        : [];

      const { documents } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
        queries
      );

      const now = new Date();

      const stats = documents.reduce(
        (acc, doc) => {
          const verification = doc as unknown as CertificateVerification;
          const expiresAt = new Date(verification.expiresAt);

          acc.totalVerifications += verification.verificationCount;

          if (verification.isRevoked) {
            acc.revokedCertificates++;
          } else if (now > expiresAt) {
            acc.expiredCertificates++;
          } else {
            acc.validCertificates++;
          }

          return acc;
        },
        {
          totalVerifications: 0,
          validCertificates: 0,
          expiredCertificates: 0,
          revokedCertificates: 0,
        }
      );

      return stats;
    } catch (error) {
      console.error("Error retrieving verification statistics:", error);
      return {
        totalVerifications: 0,
        validCertificates: 0,
        expiredCertificates: 0,
        revokedCertificates: 0,
      };
    }
  }

  /**
   * Searches certificates by criteria (for administration)
   */
  static async searchCertificates(criteria: {
    region?: string;
    commune?: string;
    quartier?: string;
    issuerName?: string;
    dateFrom?: string;
    dateTo?: string;
    status?: "valid" | "expired" | "revoked";
  }): Promise<{
    certificates: Array<{
      hash: string;
      reference: string;
      issuedAt: string;
      expiresAt: string;
      status: string;
      issuerName: string;
      location: string;
      verificationCount: number;
    }>;
    total: number;
  }> {
    try {
      const databases = await this.getDatabase();

      // Build queries for metadata search
      const metadataQueries: string[] = [];

      if (criteria.region) {
        metadataQueries.push(Query.equal("region", criteria.region));
      }
      if (criteria.commune) {
        metadataQueries.push(Query.equal("commune", criteria.commune));
      }
      if (criteria.quartier) {
        metadataQueries.push(Query.equal("quartier", criteria.quartier));
      }
      if (criteria.issuerName) {
        metadataQueries.push(Query.search("issuerName", criteria.issuerName));
      }

      // Get matching metadata
      const { documents: metadataDocuments } = await databases.listDocuments(
        DATABASE_ID,
        CERTIFICATE_METADATA_COLLECTION_ID,
        metadataQueries
      );

      if (metadataDocuments.length === 0) {
        return { certificates: [], total: 0 };
      }

      // Get verification IDs from metadata
      const verificationIds = metadataDocuments.map(
        (doc) => doc.verificationId
      );

      // Build queries for verifications
      const verificationQueries = [Query.equal("$id", verificationIds)];

      if (criteria.dateFrom) {
        verificationQueries.push(
          Query.greaterThanEqual("issuedAt", criteria.dateFrom)
        );
      }
      if (criteria.dateTo) {
        verificationQueries.push(
          Query.lessThanEqual("issuedAt", criteria.dateTo)
        );
      }

      const { documents: verificationDocuments } =
        await databases.listDocuments(
          DATABASE_ID,
          CERTIFICATE_VERIFICATIONS_COLLECTION_ID,
          verificationQueries
        );

      // Combine and filter results
      const now = new Date();
      const results = verificationDocuments
        .map((verificationDoc) => {
          const verification =
            verificationDoc as unknown as CertificateVerification;
          const metadata = metadataDocuments.find(
            (m) => m.verificationId === verification.$id
          ) as unknown as CertificateMetadata;

          if (!metadata) return null;

          const expiresAt = new Date(verification.expiresAt);
          let status = "valid";

          if (verification.isRevoked) {
            status = "revoked";
          } else if (now > expiresAt) {
            status = "expired";
          }

          return {
            hash: verification.hash,
            reference: verification.certificateId,
            issuedAt: verification.issuedAt,
            expiresAt: verification.expiresAt,
            status,
            issuerName: metadata.issuerName,
            location: `${metadata.quartier}, ${metadata.commune}, ${metadata.region}`,
            verificationCount: verification.verificationCount,
          };
        })
        .filter(Boolean)
        .filter((cert) => !criteria.status || cert!.status === criteria.status);

      return {
        certificates: results as any[],
        total: results.length,
      };
    } catch (error) {
      console.error("Error searching certificates:", error);
      return { certificates: [], total: 0 };
    }
  }
}
