"use client";

import { Shield, Phone, Mail, MapPin, Clock, Globe, ExternalLink } from "lucide-react";

export function EnhancedVerificationFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative mt-16 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
      
      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            
            {/* Government Info */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">République de Guinée</h3>
                  <p className="text-gray-300">Ministère de l'Administration du Territoire</p>
                </div>
              </div>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                Système officiel de vérification des certificats de résidence. 
                Cette plateforme garantit l'authenticité des documents émis par 
                les autorités locales de la République de Guinée.
              </p>

              {/* Tricolor Band */}
              <div className="flex h-2 rounded-full overflow-hidden mb-4">
                <div className="flex-1 bg-red-500"></div>
                <div className="flex-1 bg-yellow-400"></div>
                <div className="flex-1 bg-green-500"></div>
              </div>
              <p className="text-xs text-gray-400">Travail - Justice - Solidarité</p>
            </div>

            {/* Contact Information */}
            <div>
              <h4 className="text-lg font-semibold mb-6 flex items-center">
                <Phone className="w-5 h-5 mr-2 text-green-400" />
                Contact
              </h4>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-gray-400 mt-1 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-gray-300">
                      Ministère de l'Administration du Territoire<br />
                      Conakry, République de Guinée
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  <p className="text-sm text-gray-300">+224 XX XX XX XX</p>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  <p className="text-sm text-gray-300"><EMAIL></p>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  <p className="text-sm text-gray-300">Service 24h/24, 7j/7</p>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6 flex items-center">
                <Globe className="w-5 h-5 mr-2 text-blue-400" />
                Liens Utiles
              </h4>
              
              <div className="space-y-3">
                <a 
                  href="#" 
                  className="flex items-center space-x-2 text-sm text-gray-300 hover:text-white transition-colors duration-200 group"
                >
                  <ExternalLink className="w-4 h-4 group-hover:text-green-400" />
                  <span>Portail Gouvernemental</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex items-center space-x-2 text-sm text-gray-300 hover:text-white transition-colors duration-200 group"
                >
                  <ExternalLink className="w-4 h-4 group-hover:text-yellow-400" />
                  <span>Services Administratifs</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex items-center space-x-2 text-sm text-gray-300 hover:text-white transition-colors duration-200 group"
                >
                  <ExternalLink className="w-4 h-4 group-hover:text-red-400" />
                  <span>Guide d'Utilisation</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex items-center space-x-2 text-sm text-gray-300 hover:text-white transition-colors duration-200 group"
                >
                  <ExternalLink className="w-4 h-4 group-hover:text-blue-400" />
                  <span>Support Technique</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="border-t border-gray-700">
          <div className="max-w-7xl mx-auto px-4 py-6">
            <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-xl p-6 border border-blue-800/30">
              <div className="flex items-start space-x-4">
                <Shield className="w-8 h-8 text-blue-400 flex-shrink-0 mt-1" />
                <div>
                  <h5 className="font-semibold text-blue-300 mb-2">Avis de Sécurité Important</h5>
                  <p className="text-sm text-gray-300 leading-relaxed">
                    Ce système utilise des technologies de cryptographie avancées pour garantir 
                    l'authenticité des certificats. Seuls les documents officiellement émis par 
                    les autorités compétentes de la République de Guinée peuvent être vérifiés 
                    avec succès sur cette plateforme.
                  </p>
                  <div className="mt-3 flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-900/50 text-green-300 border border-green-700/50">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      Chiffrement SSL/TLS
                    </span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-900/50 text-blue-300 border border-blue-700/50">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                      Signature Numérique
                    </span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-900/50 text-purple-300 border border-purple-700/50">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                      Horodatage Sécurisé
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-700">
          <div className="max-w-7xl mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="text-center md:text-left mb-4 md:mb-0">
                <p className="text-sm text-gray-400">
                  © {currentYear} République de Guinée. Tous droits réservés.
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Système de vérification officiel des certificats de résidence
                </p>
              </div>
              
              <div className="flex items-center space-x-6 text-xs text-gray-500">
                <a href="#" className="hover:text-gray-300 transition-colors duration-200">
                  Politique de Confidentialité
                </a>
                <a href="#" className="hover:text-gray-300 transition-colors duration-200">
                  Conditions d'Utilisation
                </a>
                <a href="#" className="hover:text-gray-300 transition-colors duration-200">
                  Accessibilité
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
